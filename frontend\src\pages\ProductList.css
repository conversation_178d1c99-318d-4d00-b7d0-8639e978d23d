.product-list-container {
  padding: 2rem;
  background-color: #f7f8fa;
  font-family: "Segoe UI", sans-serif;
}

.product-list-container h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #222;
}

.product-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  justify-items: center;
}

.product-card {
  width: 100%;
  max-width: 320px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  padding: 1rem;
  transition: transform 0.2s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* Image container with background image */
.product-image {
  width: 100%;
  height: 180px;
  background-color: #e0e0e0;
  background-size: cover;
  background-position: center;
  border-radius: 8px;
  margin-bottom: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Placeholder text if no image */
.image-placeholder {
  font-size: 1rem;
  color: #888;
  text-align: center;
  padding: 10px;
}

.product-card h3 {
  font-size: 1.05rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.4rem;
  text-align: center;
}

.description {
  font-size: 0.9rem;
  color: #666;
  text-align: center;
  margin-bottom: 0.6rem;
  line-height: 1.4;
}

.price {
  font-size: 1rem;
  color: #d32f2f;
  font-weight: 600;
  margin-bottom: 0.4rem;
  text-align: center;
}

.shop-info {
  font-size: 0.85rem;
  color: #777;
  text-align: center;
  margin-bottom: 0.8rem;
  line-height: 1.3;
}

.actions {
  display: flex;
  gap: 10px;
  margin-top: auto;
}

.actions button {
  flex: 1;
  padding: 0.5rem 0.75rem;
  font-size: 0.85rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-add {
  background-color: #1976d2;
  color: white;
}

.btn-add:hover {
  background-color: #145ca1;
}

.btn-buy {
  background-color: #43a047;
  color: white;
}

.btn-buy:hover {
  background-color: #2e7d32;
}
