.cart-container {
  padding: 2rem;
  max-width: 900px;
  margin: auto;
}

.cart-container h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 2rem;
}

.cart-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.cart-card {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  align-items: center;
}

.cart-card:hover {
  transform: translateY(-3px);
}

.cart-image {
  width: 140px;
  height: 140px;
  background-color: #e0e0e0;
  background-size: cover;
  background-position: center;
  border-radius: 8px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-placeholder {
  font-size: 1rem;
  color: #888;
  text-align: center;
  padding: 10px;
}

.cart-info {
  flex: 1;
}

.cart-info h3 {
  margin: 0;
  font-size: 1.4rem;
}

.cart-info .description {
  font-size: 0.95rem;
  color: #555;
}

.cart-info .price {
  font-size: 1.2rem;
  color: #d9230f;
  font-weight: bold;
  margin-top: 0.5rem;
}

.cart-info .shop {
  font-size: 0.9rem;
  color: #777;
  margin-top: 0.3rem;
}

.cart-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.5rem;
}

.cart-actions button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.cart-actions button:first-child {
  background-color: #1e90ff;
  color: white;
}

.cart-actions .remove-btn {
  background-color: #ff4d4f;
  color: white;
}
