/* ---------- Container for the whole buy-now form ---------- */
.buy-now-form {
  max-width: 500px;
  margin: 2rem auto;
  padding: 2rem 2.2rem 2.5rem 2.2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
  gap: 1.8rem;
}

/* ---------- Product summary ---------- */
.product-summary {
  display: flex;
  align-items: center;
  gap: 1.4rem;
  padding: 1rem 1.4rem;
  background: #f9fbff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.1);
}

.product-summary .product-image {
  width: 140px;
  height: 140px;
  object-fit: contain;
  border-radius: 12px;
  border: 1.8px solid #d0e2ff;
  background: #fff;
  box-shadow: 0 3px 10px rgba(0, 123, 255, 0.15);
  flex-shrink: 0;
}

.product-details { flex: 1; }

.product-details .price {
  font-size: 1.4rem;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 0.6rem;
}

.product-details p {
  font-size: 1.05rem;
  line-height: 1.4;
  color: #444;
  margin: 0;
}

/* ---------- Confirm button ---------- */
.btn-confirm {
  display: block;
  width: 100%;
  background: #007bff;
  color: #fff;
  font-weight: 700;
  padding: 0.8rem 0;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1.1rem;
  transition: background 0.3s;
  user-select: none;
}
.btn-confirm:hover { background: #0056b3; }

/* ---------- UPI scanner ---------- */
.upi-scanner {
  margin-top: 1.2rem;
  text-align: center;
  background: #f0f8ff;
  padding: 1.5rem 1rem;
  border-radius: 14px;
  box-shadow: 0 3px 12px rgba(0, 123, 255, 0.15);
}

.scanner-image {
  width: 220px;
  height: 220px;
  margin: 1rem auto 1.2rem;
  border-radius: 12px;
  border: 2.5px solid #007bff;
  box-shadow: 0 5px 18px rgba(0, 123, 255, 0.3);
  background: #fff;
  display: block;
}

.note {
  font-size: 0.9rem;
  color: #555;
  font-style: italic;
}

/* ---------- Success splash ---------- */
.buy-now-container.success {
  max-width: 480px;
  margin: 3rem auto;
  padding: 2.5rem 2rem;
  background: #e0f7fa;
  border-radius: 14px;
  box-shadow: 0 8px 30px rgba(0, 150, 136, 0.2);
  text-align: center;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  color: #00695c;
}

.buy-now-container.success h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6rem;
}

.success-icon { color: #00796b; font-size: 2.5rem; }

.buy-now-container.success p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  font-weight: 600;
}

.btn-home {
  background: #00796b;
  color: #fff;
  padding: 0.9rem 2rem;
  border-radius: 10px;
  border: none;
  cursor: pointer;
  font-weight: 700;
  font-size: 1.1rem;
  transition: background 0.3s;
  user-select: none;
}
.btn-home:hover { background: #004d40; }

/* ---------- Form labels / inputs ---------- */
.buy-now-form label {
  display: flex;
  flex-direction: column;
  font-weight: 600;
  font-size: 1rem;
  color: #333;
  gap: 0.5rem;
}

.buy-now-form input[type="text"],
.buy-now-form input[type="email"],
.buy-now-form input[type="number"],
.buy-now-form textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border: 1.8px solid #ddd;
  border-radius: 10px;
  transition: border-color 0.3s, box-shadow 0.3s;
  font-family: inherit;
  box-shadow: 0 2px 5px rgb(0 0 0 / 0.05);
  resize: vertical;
}

.buy-now-form input:focus,
.buy-now-form textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.4);
}

.buy-now-form textarea { min-height: 90px; }

/* ---------- Payment method buttons ---------- */
.payment-method p {
  font-weight: 700;
  margin-bottom: 1rem;
  font-size: 1.15rem;
  color: #222;
}

.payment-options {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.payment-options button {
  flex: 1;
  padding: 0.9rem 0;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: 12px;
  border: 2.5px solid #ccc;
  background: #f9f9f9;
  cursor: pointer;
  transition: all 0.3s;
  color: #555;
  user-select: none;
  box-shadow: 0 3px 8px rgb(0 0 0 / 0.05);
  text-align: center;
}

.payment-options button.active {
  background: #007bff;
  color: #fff;
  border-color: #007bff;
  box-shadow: 0 0 15px rgba(0, 123, 255, 0.6);
}

.payment-options button:hover:not(.active) {
  border-color: #007bff;
  color: #007bff;
  box-shadow: 0 0 12px rgba(0, 123, 255, 0.4);
}

/* ---------- Elegant file picker ---------- */
.file-input-wrapper {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}

.file-input-wrapper input[type="file"] { display: none; }

.file-label {
  background: #4a6cf7;
  color: #fff;
  padding: 8px 18px;
  border-radius: 8px;
  font-size: 0.92rem;
  cursor: pointer;
  transition: background 0.25s;
  user-select: none;
}
.file-label:hover { background: #385be0; }

.file-preview {
  width: 220px;
  height: auto;
  border: 1px solid #e1e4f2;
  border-radius: 10px;
  object-fit: contain;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}
