.create-user-form {
  max-width: 450px;
  margin: 4rem auto;
  padding: 0.5rem 3.5rem;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 18px;
  box-shadow:
    0 8px 32px rgba(31, 38, 135, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.18);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  font-family: 'Montserrat', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #222;
  transition: box-shadow 0.4s ease;
}

.create-user-form:hover {
  box-shadow:
    0 12px 50px rgba(31, 38, 135, 0.15),
    0 0 0 1.5px rgba(255, 255, 255, 0.25);
}

.create-user-form h2 {
  text-align: center;
  margin-bottom: 2rem;
  font-weight: 900;
  font-size: 2.4rem;
  letter-spacing: 0.08em;
  color: #1a1a40;
  text-transform: uppercase;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.create-user-form label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 700;
  font-size: 1.05rem;
  color: #444a72;
  letter-spacing: 0.04em;
  text-transform: uppercase;
}

.create-user-form select,
.create-user-form input[type="text"],
.create-user-form input[type="email"],
.create-user-form input[type="password"] {
  width: 100%;
  padding: 0.75rem 1.3rem;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  border-radius: 14px;
  border: 2px solid #c3cde6;
  background: #f8f9ff;
  box-shadow: inset 2px 2px 8px #d0d9ff, inset -2px -2px 8px #ffffff;
  transition: border-color 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  font-family: inherit;
  box-sizing: border-box;
  color: #2a2e45;
  font-weight: 600;
}

.create-user-form select:hover,
.create-user-form input[type="text"]:hover,
.create-user-form input[type="email"]:hover,
.create-user-form input[type="password"]:hover {
  background: #e3e9ff;
  border-color: #7b8dff;
  box-shadow: inset 2px 2px 10px #9aaaff, inset -2px -2px 10px #d0dbff;
  cursor: pointer;
}

.create-user-form select:focus,
.create-user-form input[type="text"]:focus,
.create-user-form input[type="email"]:focus,
.create-user-form input[type="password"]:focus {
  outline: none;
  background: #e6ecff;
  border-color: #4058ff;
  box-shadow:
    0 0 12px 2px rgba(64, 88, 255, 0.5),
    inset 1px 1px 10px #5c72ff,
    inset -1px -1px 10px #aabaff;
  color: #1c1f3a;
}

.create-user-form button {
  width: 100%;
  padding: 0.85rem 0;
  background: linear-gradient(135deg, #5a6bff, #3949f7);
  border: none;
  border-radius: 20px;
  color: #f0f4ff;
  font-weight: 900;
  font-size: 1.2rem;
  letter-spacing: 0.08em;
  cursor: pointer;
  box-shadow:
    0 8px 20px rgba(59, 73, 255, 0.7),
    0 0 12px 3px rgba(59, 73, 255, 0.4);
  transition: background 0.3s ease, box-shadow 0.3s ease;
  user-select: none;
  text-transform: uppercase;
  font-family: 'Montserrat', sans-serif;
}

.create-user-form button:hover {
  background: linear-gradient(135deg, #2438ff, #1b27e9);
  box-shadow:
    0 12px 35px rgba(36, 56, 255, 0.85),
    0 0 25px 8px rgba(36, 56, 255, 0.6);
}

.create-user-form p {
  margin-top: 1.8rem;
  text-align: center;
  font-weight: 700;
  font-size: 1rem;
  color: #28a745;
  letter-spacing: 0.04em;
  user-select: none;
  text-shadow: 0 1px 1px rgba(0,0,0,0.1);
}
