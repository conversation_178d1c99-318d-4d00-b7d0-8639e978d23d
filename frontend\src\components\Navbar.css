/* Navbar container */
.navbar {
  background-color: #2c3e50; /* Darker, elegant blue-gray */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1.5rem;
  height: 3.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  color: #ecf0f1;
  position: sticky;
  top: 0;
  z-index: 1000;
}

/* Left and right menu lists */
.navbar-left,
.navbar-right {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Left menu links */
.navbar-left li a {
  color: #ecf0f1;
  font-weight: 600;
  font-size: 1.1rem;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: background-color 0.3s ease, color 0.3s ease;
  position: relative; /* Required for badge positioning */
}

.navbar-left li a:hover,
.navbar-left li a:focus {
  background-color: #34495e;
  color: #1abc9c; /* Accent mint */
  outline: none;
}

/* Notification badge */
.badge {
  background-color: red;
  color: white;
  font-size: 0.7rem;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 50%;
  position: absolute;
  top: 4px;
  right: 6px;
  display: inline-block;
}

/* Right menu links */
.navbar-right li {
  margin-left: 1rem;
}

.navbar-right li a {
  color: #ecf0f1;
  font-weight: 500;
  font-size: 1rem;
  text-decoration: none;
  padding: 0.5rem 0.9rem;
  border-radius: 6px;
  transition: background-color 0.3s ease, color 0.3s ease;
  display: flex;
  align-items: center;
  height: 2.8rem;
}

.navbar-right li a:hover,
.navbar-right li a:focus {
  background-color: #16a085;
  color: #fff;
  outline: none;
}

/* Logout button styling */
.logout-button {
  background-color: transparent;
  border: 2px solid #e74c3c;
  color: #e74c3c;
  padding: 0.45rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: background-color 0.3s ease, color 0.3s ease;
  height: 2.8rem;
  display: flex;
  align-items: center;
}

.logout-button:hover,
.logout-button:focus {
  background-color: #e74c3c;
  color: #fff;
  outline: none;
}

/* Subtle hover effect on list items for easier navigation */
.navbar-left li:hover,
.navbar-right li:hover {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

/* Responsive for smaller screens */
@media (max-width: 600px) {
  .navbar {
    flex-direction: column;
    height: auto;
    padding: 1rem;
  }

  .navbar-left,
  .navbar-right {
    flex-wrap: wrap;
    justify-content: center;
  }

  .navbar-right li {
    margin-left: 0.5rem;
    margin-top: 0.5rem;
  }
}
