.profile-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 70px);
  background-color: #f0f4f8;
  padding: 2rem;
}

.profile-card {
  background-color: #fff;
  padding: 2rem 3rem;
  border-radius: 12px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
}

.profile-card h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
}

.profile-field {
  margin-bottom: 1rem;
}

.profile-field label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.3rem;
  color: #444;
}

.profile-field p,
.profile-field input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
  color: #333;
}

.profile-field p {
  background-color: #f7f9fb;
}

.profile-card button {
  margin-top: 1rem;
  width: 100%;
  padding: 0.75rem;
  background-color: #007bff;
  border: none;
  color: white;
  font-size: 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.profile-card button:hover {
  background-color: #0056b3;
}
