/* ---------- Container ---------- */
.product-list {
  max-width: 820px;
  margin: 2.5rem auto;
  padding: 1.4rem 1.8rem;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.05);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.product-list h2 {
  text-align: center;
  margin-bottom: 1.6rem;
  font-size: 1.8rem;
  color: #2c3e50;
}

/* ---------- List ---------- */
.products-ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 18px;
  padding: 14px 0;
  border-bottom: 1px solid #ececec;
  transition: background 0.25s;
}
.product-item:last-child { border-bottom: none; }

.product-item:hover {
  background: #f9fbff;
}

/* ---------- Thumbnail ---------- */
.product-thumb {
  width: 72px;
  height: 72px;
  object-fit: cover;
  border-radius: 10px;
  background: #fafafa;
  border: 1px solid #e0e0e0;
  flex-shrink: 0;
}

/* ---------- Info ---------- */
.product-info {
  flex: 1;
  font-size: 0.97rem;
}

.product-info strong {
  display: block;
  font-size: 1.05rem;
  margin-bottom: 4px;
  color: #34495e;
}

.product-info small {
  color: #6b6b6b;
  line-height: 1.4;
}

/* ---------- Error text (optional) ---------- */
.error-message {
  color: #dc3545;
  text-align: center;
  margin-top: 1rem;
}
