/* AdminDashboard.css */

.admin-dashboard {
  max-width: 600px;
  margin: 50px auto;
  padding: 40px;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  text-align: center;
  transition: transform 0.3s ease;
}

.admin-dashboard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.admin-dashboard h2 {
  font-size: 2.8rem;
  margin-bottom: 15px;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-shadow: 1px 1px 4px rgba(0,0,0,0.3);
}

.admin-dashboard p {
  font-size: 1.25rem;
  margin-bottom: 30px;
  line-height: 1.5;
  opacity: 0.9;
}

.admin-button {
  background-color: #ff6f61;
  border: none;
  padding: 15px 35px;
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  color: white;
  box-shadow: 0 6px 15px rgba(255, 111, 97, 0.6);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  user-select: none;
}

.admin-button:hover {
  background-color: #ff3b2e;
  box-shadow: 0 8px 25px rgba(255, 59, 46, 0.8);
}

.admin-button:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(255, 111, 97, 0.5);
}
