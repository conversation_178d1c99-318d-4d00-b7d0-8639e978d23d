import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";

/* ───────── Common Components ───────── */
import Navbar from "./components/Navbar";

/* ───────── Auth helpers ───────── */
import { getToken, getUserRoles } from "./utils/auth";

/* ───────── Pages ───────── */
// Auth / profile
import Login            from "./pages/Login";
import Register         from "./pages/Register";
import Profile          from "./pages/Profile";

// Seller
import SellerStatus     from "./pages/SellerStatus";
import AddShop          from "./pages/AddShop";
import MyShops          from "./pages/MyShops";
import AddProduct       from "./pages/AddProduct";
import MyProducts       from "./pages/MyProducts";
import PendingOrders    from "./pages/PendingOrders";
import ConfirmedOrders  from "./pages/ConfirmedOrders";

// Admin & super-admin
import AdminDashboard   from "./pages/AdminDashboard";
import CreateUser       from "./pages/CreateUser";
import PendingAdmins    from "./pages/PendingAdmins";
import ApprovedAdmins   from "./pages/ApprovedAdmins";
import AdminStatus      from "./pages/AdminStatus";
import PendingSellers   from "./pages/PendingSellers";
import ApprovedSellers  from "./pages/ApprovedSellers";
import PendingShops     from "./pages/PendingShops";
import ApprovedShops    from "./pages/ApprovedShops";

// User shopping
import ProductList      from "./pages/ProductList";
import BuyNow           from "./pages/BuyNow";
import Cart             from "./pages/Cart";

// User orders / notifications
import UserOrders       from "./pages/UserOrders";
import UserNotifications from "./pages/UserNotifications";

/* ───────── Constants & Configuration ───────── */
const ROLES = {
  USER: "ROLE_USER",
  SELLER: "ROLE_SELLER",
  ADMIN: "ROLE_ADMIN",
  SUPER_ADMIN: "ROLE_SUPER_ADMIN"
};

const ROLE_HOME_ROUTES = {
  [ROLES.SELLER]: "/seller-profile",
  [ROLES.USER]: "/product-list",
  [ROLES.ADMIN]: "/pending",
  [ROLES.SUPER_ADMIN]: "/admin-dashboard"
};

// Route configuration - centralized route definitions
const ROUTE_CONFIG = [
  // Public routes
  { path: "/login", component: Login, isPublic: true },
  { path: "/register", component: Register, isPublic: true },

  // User routes
  { path: "/product-list", component: ProductList, roles: [ROLES.USER] },
  { path: "/cart", component: Cart, roles: [ROLES.USER] },
  { path: "/user-orders", component: UserOrders, roles: [ROLES.USER] },
  { path: "/user-notifications", component: UserNotifications, roles: [ROLES.USER] },

  // Multi-role routes
  { path: "/buy-now", component: BuyNow, roles: [ROLES.USER, ROLES.SELLER, ROLES.ADMIN, ROLES.SUPER_ADMIN] },
  { path: "/profile", component: Profile, roles: [ROLES.USER, ROLES.SELLER, ROLES.ADMIN, ROLES.SUPER_ADMIN] },

  // Seller routes
  { path: "/seller-profile", component: SellerStatus, roles: [ROLES.SELLER] },
  { path: "/add-shop", component: AddShop, roles: [ROLES.SELLER] },
  { path: "/my-shops", component: MyShops, roles: [ROLES.SELLER] },
  { path: "/add-product", component: AddProduct, roles: [ROLES.SELLER] },
  { path: "/my-products", component: MyProducts, roles: [ROLES.SELLER] },
  { path: "/seller-pending-orders", component: PendingOrders, roles: [ROLES.SELLER] },
  { path: "/seller-confirmed-orders", component: ConfirmedOrders, roles: [ROLES.SELLER] },

  // Admin/Super-Admin shared routes
  { path: "/pending", component: PendingSellers, roles: [ROLES.ADMIN, ROLES.SUPER_ADMIN] },
  { path: "/approved", component: ApprovedSellers, roles: [ROLES.ADMIN, ROLES.SUPER_ADMIN] },
  { path: "/pending-shops", component: PendingShops, roles: [ROLES.ADMIN, ROLES.SUPER_ADMIN] },
  { path: "/approved-shops", component: ApprovedShops, roles: [ROLES.ADMIN, ROLES.SUPER_ADMIN] },
  { path: "/admin-status", component: AdminStatus, roles: [ROLES.ADMIN, ROLES.SUPER_ADMIN] },
  { path: "/approved-admins", component: ApprovedAdmins, roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN] },

  // Super-Admin only routes
  { path: "/admin-dashboard", component: AdminDashboard, roles: [ROLES.SUPER_ADMIN] },
  { path: "/create-user", component: CreateUser, roles: [ROLES.SUPER_ADMIN] },
  { path: "/pending-admins", component: PendingAdmins, roles: [ROLES.SUPER_ADMIN] }
];

/* ───────── Helper Functions ───────── */
function getHomeRouteForRoles(roles) {
  for (const role of roles) {
    if (ROLE_HOME_ROUTES[role]) {
      return ROLE_HOME_ROUTES[role];
    }
  }
  return "/login";
}

/* ───────── Role-based landing helper ───────── */
function HomeRedirect() {
  const roles = getUserRoles();
  const homeRoute = getHomeRouteForRoles(roles);
  return <Navigate to={homeRoute} replace />;
}

/* ───────── Route wrappers ───────── */
const ProtectedRoute = ({ element, allowedRoles }) => {
  const token = getToken();
  const roles = getUserRoles();
  if (!token) return <Navigate to="/login" replace />;
  const hasPermission = roles.some(role => allowedRoles.includes(role));
  return hasPermission ? element : <Navigate to="/unauthorized" replace />;
};

const PublicRoute = ({ element }) => {
  const token = getToken();
  const roles = getUserRoles();
  if (!token) return element; // still logged-out

  // already logged-in → redirect to role home
  const homeRoute = getHomeRouteForRoles(roles);
  return <Navigate to={homeRoute} replace />;
};

// Helper to create route elements
const createRouteElement = (routeConfig) => {
  const { component: Component, roles, isPublic } = routeConfig;

  if (isPublic) {
    return <PublicRoute element={<Component />} />;
  }

  return <ProtectedRoute element={<Component />} allowedRoles={roles} />;
};

/* ───────── Fallback page ───────── */
const Unauthorized = () => (
  <div style={{ padding:"2rem", textAlign:"center" }}>
    <h2>Access Denied</h2>
    <p>You do not have permission to view this page.</p>
    <a href="/">Go back home</a>
  </div>
);

/* ───────── App Root ───────── */
function App() {
  return (
    <Router>
      <Navbar />

      <Routes>
        {/* Dynamic routes from configuration */}
        {ROUTE_CONFIG.map((routeConfig) => (
          <Route
            key={routeConfig.path}
            path={routeConfig.path}
            element={createRouteElement(routeConfig)}
          />
        ))}

        {/* --- Role-based landing */}
        <Route
          path="/"
          element={
            <ProtectedRoute
              element={<HomeRedirect />}
              allowedRoles={Object.values(ROLES)}
            />
          }
        />

        {/* --- Fallbacks */}
        <Route path="/unauthorized" element={<Unauthorized />} />
        <Route path="*" element={<h2 style={{ textAlign:"center", marginTop:"3rem" }}>404: Page Not Found</h2>} />
      </Routes>
    </Router>
  );
}

export default App;
