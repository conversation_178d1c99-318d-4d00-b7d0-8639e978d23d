/* ---------- Container & grid stay unchanged ---------- */
.pending-orders-container {
  max-width: 1200px;
  margin: 2rem auto;
  font-family: "Segoe UI", sans-serif;
}

h2 { margin-bottom: 1.2rem; }

.orders-grid {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
}

/* ---------- Card layout ---------- */
.order-card {
  display: flex;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 6px 18px rgba(0,0,0,0.08);
  overflow: hidden;
}

.left-col, .right-col {
  padding: 1rem 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.left-col {
  border-right: 1px solid #eee;
  width: 40%;
}

.right-col {
  width: 60%;
  align-items: flex-start;
}

.product-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: 10px;
}

.product-title {
  margin: .75rem 0 1rem;
  font-weight: 600;
  text-align: center;
}

/* ---------- Confirm button ---------- */
.btn-confirm {
  background: #34c759;
  color: #fff;
  border: none;
  padding: .55rem 1.2rem;
  border-radius: 8px;
  cursor: pointer;
  transition: background .25s;
}
.btn-confirm:hover { background: #28a94f; }

/* ---------- Screenshot toggle ----------
   Uses <details><summary>…</summary><img /></details>  */
details.screenshot-toggle {
  width: 100%;
  margin-top: 0.75rem;
}

/* Summary line */
details.screenshot-toggle summary {
  cursor: pointer;
  display: inline-block;
  padding: .35rem .6rem;
  background: #f1f1f1;
  border-radius: 6px;
  font-size: .9rem;
  user-select: none;
}

/* Add a chevron */
details.screenshot-toggle summary::after {
  content: " ⬇︎";
  font-size: .8rem;
}
details.screenshot-toggle[open] summary::after {
  content: " ▲";
}

/* The image itself */
details.screenshot-toggle img {
  width: 100%;
  max-height: 460px;          /* full-view but not endlessly tall */
  object-fit: contain;        /* show entire screenshot */
  background: #f9f9f9;
  border-radius: 10px;
  margin-top: 0.6rem;
}

/* ---------- Misc ---------- */
.center { text-align: center; margin-top: 2rem; }
